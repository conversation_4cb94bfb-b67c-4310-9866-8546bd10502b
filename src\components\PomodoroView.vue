<template>
  <div class="pomodoro-view">
    <!-- Navigation Bar - Exact same as habit tracker -->
    <nav class="nav-container">
      <div
        class="logo"
        @click="$emit('back-to-habits')"
        style="cursor: pointer"
      >
        <div class="logo-icon"></div>
        <span style="margin-bottom: 7px">ANEW</span>
      </div>

      <div class="nav-tabs">
        <div
          v-for="tab in tabs"
          :key="tab"
          class="nav-tab"
          :class="{ active: activeTab === tab }"
          @click="activeTab = tab"
        >
          {{ tab }}
        </div>
      </div>

      <div class="nav-buttons">
        <button
          class="sort-button"
          @click="showSortModal = true"
          title="Sort Projects"
        >
          <svg
            class="sort-icon"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 7L12 3L16 7"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 3V15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16 17L12 21L8 17"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 21V9"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <button class="add-button" @click="showAddModal = true">+</button>
        <button class="statistics-button" @click="showStatisticsModal = true">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M22,21H2V3H4V19H6V10H10V19H12V6H16V19H18V14H22V21Z" />
          </svg>
        </button>
        <button class="settings-button" @click="showSettingsModal = true">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"
            />
          </svg>
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <div class="projects-grid">
        <ProjectCard
          v-for="project in filteredProjects"
          :key="project.id"
          :project="project"
          @open-project="openProject"
          @edit-project="editProject"
          @complete-project="completeProject"
          @delete-project="deleteProject"
        />
      </div>
    </main>

    <!-- Modals -->
    <AddProjectModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @add-project="addProject"
      :error-message="addProjectError"
      :default-category="activeTab"
    />

    <ProjectViewModal
      v-if="showProjectModal"
      :project="selectedProject"
      @close="showProjectModal = false"
      @update-project="updateProject"
      @start-timer="startTimerForTask"
    />

    <SortProjectsModal
      v-if="showSortModal"
      :projects="projects"
      :activeTab="activeTab"
      @close="showSortModal = false"
      @save-order="saveProjectOrder"
    />

    <PomodoroSettingsModal
      v-if="showSettingsModal"
      @close="showSettingsModal = false"
      @save="handleSettingsSave"
    />

    <StatisticsModal
      v-if="showStatisticsModal"
      @close="showStatisticsModal = false"
    />

    <!-- Pomodoro Timer - Always show if there's a current or last task -->
    <PomodoroTimer
      v-if="currentTimerTask"
      :task="currentTimerTask"
      :initialTime="pomodoroSettings.pomodoroLength * 60"
      :settings="pomodoroSettings"
      @timer-complete="handleTimerComplete"
      @timer-stop="handleTimerStop"
      @timer-start="handleTimerStart"
      @minimize="handleTimerMinimize"
      @maximize="handleTimerMaximize"
    />
  </div>
</template>

<script>
import { ref, onMounted, computed } from "vue";
import ProjectCard from "./ProjectCard.vue";
import AddProjectModal from "./AddProjectModal.vue";
import ProjectViewModal from "./ProjectViewModal.vue";
import SortProjectsModal from "./SortProjectsModal.vue";
import PomodoroSettingsModal from "./PomodoroSettingsModal.vue";
import StatisticsModal from "./StatisticsModal.vue";
import PomodoroTimer from "./PomodoroTimer.vue";

export default {
  name: "PomodoroView",
  components: {
    ProjectCard,
    AddProjectModal,
    ProjectViewModal,
    SortProjectsModal,
    PomodoroSettingsModal,
    StatisticsModal,
    PomodoroTimer,
  },
  emits: ["back-to-habits"],
  setup() {
    const activeTab = ref("DAILY");
    const showAddModal = ref(false);
    const showSortModal = ref(false);
    const showProjectModal = ref(false);
    const showSettingsModal = ref(false);
    const showStatisticsModal = ref(false);
    const projects = ref([]);
    const selectedProject = ref(null);
    const addProjectError = ref("");

    // Timer state
    const currentTimerTask = ref(null);
    const lastExecutedTask = ref(null);
    const timerStartTime = ref(null);

    // Settings state
    const pomodoroSettings = ref({
      pomodoroLength: 25,
      shortBreakLength: 5,
      longBreakLength: 15,
      longBreakAfter: 4,
      autoStartPomodoro: false,
      autoStartBreak: false,
      startAlarm: "beep",
      breakAlarm: "beep",
    });

    const tabs = ["DAILY", "CODING", "COURSES", "SCHOOL", "LIFE", "COMPLETED"];

    // Computed property to filter projects by active tab
    const filteredProjects = computed(() => {
      const filtered = projects.value.filter(
        (project) => project.category === activeTab.value
      );
      const sorted = filtered.sort(
        (a, b) => (a.order_position || 0) - (b.order_position || 0)
      );

      console.log(
        `📋 Filtered projects for ${activeTab.value}:`,
        sorted.map((p) => ({
          id: p.id,
          name: p.name,
          order_position: p.order_position,
        }))
      );

      return sorted;
    });

    // Load projects from server
    const loadProjects = async () => {
      try {
        const response = await fetch("/api/projects");
        const data = await response.json();
        if (data.success) {
          console.log(
            "🔍 Loaded projects from server:",
            data.projects.map((p) => ({
              id: p.id,
              name: p.name,
              order_position: p.order_position,
            }))
          );

          // Check if we need to initialize order positions per category
          console.log(
            "🔥 FRONTEND: Starting order position initialization check"
          );
          const projectsByCategory = {};
          data.projects.forEach((project) => {
            if (!projectsByCategory[project.category]) {
              projectsByCategory[project.category] = [];
            }
            projectsByCategory[project.category].push(project);
          });

          let needsInitialization = false;
          const projectOrdersToUpdate = [];

          // Check each category separately and assign proper order positions
          Object.keys(projectsByCategory).forEach((category) => {
            const categoryProjects = projectsByCategory[category];

            // Check if this category needs initialization
            // We need to check if there are gaps in the order positions or multiple projects with the same position
            const orderPositions = categoryProjects.map(
              (p) => p.order_position || 0
            );
            const uniquePositions = [...new Set(orderPositions)];
            const hasGaps = uniquePositions.length !== categoryProjects.length;
            const hasDuplicates =
              orderPositions.length !== uniquePositions.length;
            const hasNullUndefined = categoryProjects.some(
              (p) => p.order_position === null || p.order_position === undefined
            );

            const categoryNeedsInit =
              hasNullUndefined || hasGaps || hasDuplicates;

            console.log(
              `🔍 Category ${category} needs initialization:`,
              categoryNeedsInit
            );
            console.log(
              `🔍 Category ${category} projects:`,
              categoryProjects.map((p) => ({
                id: p.id,
                name: p.name,
                order_position: p.order_position,
              }))
            );

            if (categoryNeedsInit) {
              needsInitialization = true;
              // Sort by current order_position first, then by ID for consistency
              categoryProjects.sort((a, b) => {
                if (a.order_position !== b.order_position) {
                  return (a.order_position || 0) - (b.order_position || 0);
                }
                return a.id - b.id;
              });

              // Assign sequential order positions within this category
              categoryProjects.forEach((project, index) => {
                projectOrdersToUpdate.push({
                  id: project.id,
                  order_position: index,
                });
                project.order_position = index; // Update immediately for UI
              });
            }
          });

          if (needsInitialization && projectOrdersToUpdate.length > 0) {
            console.log(
              "🔧 Initializing order positions for projects:",
              projectOrdersToUpdate
            );

            try {
              const response = await fetch("/api/projects/reorder", {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  projectOrders: projectOrdersToUpdate,
                }),
              });

              const result = await response.json();
              if (result.success) {
                console.log("✅ Order positions initialized successfully");
              } else {
                console.error(
                  "❌ Failed to initialize order positions:",
                  result.error
                );
              }
            } catch (error) {
              console.error("❌ Error initializing order positions:", error);
            }
          }

          // Ensure all projects have order_position values
          const projectsWithOrder = data.projects.map((project, index) => ({
            ...project,
            order_position:
              project.order_position !== undefined &&
              project.order_position !== null
                ? project.order_position
                : index,
          }));

          projects.value = projectsWithOrder;
          console.log(
            "Projects with order assigned:",
            projectsWithOrder.map((p) => ({
              id: p.id,
              name: p.name,
              order_position: p.order_position,
            }))
          );
        }
      } catch (error) {
        console.error("Error loading projects:", error);
      }
    };

    // Add new project
    const addProject = async (projectData) => {
      addProjectError.value = "";
      try {
        const response = await fetch("/api/projects", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(projectData),
        });
        const data = await response.json();
        if (data.success) {
          projects.value.push(data.project);
          showAddModal.value = false;
        } else {
          addProjectError.value = data.error || "Failed to add project";
        }
      } catch (error) {
        console.error("Error adding project:", error);
        addProjectError.value = "Failed to add project. Please try again.";
      }
    };

    // Open project view
    const openProject = (project) => {
      selectedProject.value = project;
      showProjectModal.value = true;
    };

    // Edit project
    const editProject = async (projectData) => {
      try {
        const response = await fetch(`/api/projects/${projectData.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(projectData),
        });
        const data = await response.json();
        if (data.success) {
          const projectIndex = projects.value.findIndex(
            (p) => p.id === projectData.id
          );
          if (projectIndex !== -1) {
            projects.value[projectIndex] = {
              ...projects.value[projectIndex],
              ...data.project,
            };
          }
        }
      } catch (error) {
        console.error("Error editing project:", error);
      }
    };

    // Complete project (move to COMPLETED category)
    const completeProject = async (projectId) => {
      try {
        const response = await fetch(`/api/projects/${projectId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ category: "COMPLETED" }),
        });
        const data = await response.json();
        if (data.success) {
          const projectIndex = projects.value.findIndex(
            (p) => p.id === projectId
          );
          if (projectIndex !== -1) {
            projects.value[projectIndex].category = "COMPLETED";
          }
        }
      } catch (error) {
        console.error("Error completing project:", error);
      }
    };

    // Delete project
    const deleteProject = async (projectId) => {
      try {
        const response = await fetch(`/api/projects/${projectId}`, {
          method: "DELETE",
        });
        const data = await response.json();
        if (data.success) {
          projects.value = projects.value.filter((p) => p.id !== projectId);
        }
      } catch (error) {
        console.error("Error deleting project:", error);
      }
    };

    // Update project from modal
    const updateProject = (updatedProject) => {
      const projectIndex = projects.value.findIndex(
        (p) => p.id === updatedProject.id
      );
      if (projectIndex !== -1) {
        projects.value[projectIndex] = updatedProject;
      }
    };

    // Save project order from sort modal
    const saveProjectOrder = async (sortedProjects) => {
      console.log("💾 Saving project order from sort modal:", sortedProjects);

      try {
        // Update the projects array with new order positions
        sortedProjects.forEach((sortedProject, index) => {
          const projectIndex = projects.value.findIndex(
            (p) => p.id === sortedProject.id
          );
          if (projectIndex !== -1) {
            projects.value[projectIndex].order_position = index;
          }
        });

        // Create the order array for the server
        const projectOrders = sortedProjects.map((project, index) => ({
          id: project.id,
          order_position: index,
        }));

        console.log("🔄 Sending new order to server:", projectOrders);

        const response = await fetch("/api/projects/reorder", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            projectOrders: projectOrders,
          }),
        });

        const data = await response.json();
        if (data.success) {
          console.log("✅ Project order saved successfully");
          showSortModal.value = false;

          // Force reactivity update
          projects.value = [...projects.value];
        } else {
          console.error("❌ Failed to save project order:", data.error);
        }
      } catch (error) {
        console.error("❌ Error saving project order:", error);
      }
    };

    // Timer handler functions
    const startTimerForTask = (task) => {
      const taskWithProject = {
        ...task,
        project_name:
          projects.value.find((p) => p.id === task.project_id)?.name ||
          "Unknown Project",
      };
      currentTimerTask.value = taskWithProject;
      lastExecutedTask.value = taskWithProject;
      saveLastExecutedTask(taskWithProject);
    };

    const handleTimerComplete = (task) => {
      // Calculate focus time (full pomodoro completed)
      if (timerStartTime.value && currentTimerTask.value) {
        const focusTime = pomodoroSettings.value.pomodoroLength * 60; // Full pomodoro time
        saveFocusTime(currentTimerTask.value, focusTime);
      }

      alert(`Pomodoro completed for: ${task?.name}`);
      // Keep the task displayed but mark as completed
      if (currentTimerTask.value) {
        currentTimerTask.value = {
          ...currentTimerTask.value,
          isCompleted: true,
          isRunning: false,
        };
      }
      timerStartTime.value = null;
    };

    const handleTimerStart = (task) => {
      // Set the start time when timer actually starts running
      timerStartTime.value = Date.now();
      console.log("Timer started for task:", task?.name);
    };

    const handleTimerStop = () => {
      // Calculate partial focus time when timer is stopped
      if (timerStartTime.value && currentTimerTask.value) {
        const elapsedTime = Math.floor(
          (Date.now() - timerStartTime.value) / 1000
        );
        if (elapsedTime > 60) {
          // Only save if more than 1 minute of focus time
          saveFocusTime(currentTimerTask.value, elapsedTime);
        }
      }

      // Completely stop the task and reset to last executed task state
      if (currentTimerTask.value) {
        currentTimerTask.value = {
          ...currentTimerTask.value,
          isRunning: false,
          isCompleted: false,
        };
      }
      timerStartTime.value = null;
    };

    const handleTimerMinimize = () => {
      // Timer is now minimized, but still running
    };

    const handleTimerMaximize = () => {
      // Timer is now maximized
    };

    // Load settings from localStorage
    const loadSettings = () => {
      try {
        const saved = localStorage.getItem("pomodoroSettings");
        if (saved) {
          const savedSettings = JSON.parse(saved);
          pomodoroSettings.value = {
            ...pomodoroSettings.value,
            ...savedSettings,
          };
        }
      } catch (error) {
        console.error("Error loading settings:", error);
      }
    };

    // Focus time tracking functions
    const saveFocusTime = (task, duration) => {
      try {
        const focusData = JSON.parse(
          localStorage.getItem("focusTimeData") || "[]"
        );

        const entry = {
          date: new Date().toISOString(),
          taskId: task.id,
          taskName: task.name,
          projectId: task.project_id,
          projectName: task.project_name || "Unknown Project",
          duration: duration, // in seconds
          completed: duration >= pomodoroSettings.value.pomodoroLength * 60, // full pomodoro completed
        };

        focusData.push(entry);
        localStorage.setItem("focusTimeData", JSON.stringify(focusData));

        console.log("Focus time saved:", entry);
      } catch (error) {
        console.error("Error saving focus time:", error);
      }
    };

    // Handle settings save
    const handleSettingsSave = (settings) => {
      console.log("Settings saved:", settings);
      pomodoroSettings.value = settings;
      // Settings are already saved to localStorage by the modal
    };

    // Load last executed task from localStorage
    const loadLastExecutedTask = () => {
      try {
        const saved = localStorage.getItem("lastExecutedTask");
        if (saved) {
          const task = JSON.parse(saved);
          currentTimerTask.value = {
            ...task,
            isRunning: false,
            isCompleted: false,
          };
          lastExecutedTask.value = task;
          return;
        }
      } catch (error) {
        console.error("Error loading last executed task:", error);
      }

      // Default task if no saved task
      currentTimerTask.value = {
        id: "default",
        name: "No task selected",
        project_name: "Select a task to start",
        isRunning: false,
        isCompleted: false,
      };
    };

    // Save last executed task to localStorage
    const saveLastExecutedTask = (task) => {
      try {
        localStorage.setItem("lastExecutedTask", JSON.stringify(task));
      } catch (error) {
        console.error("Error saving last executed task:", error);
      }
    };

    onMounted(() => {
      loadProjects();
      loadLastExecutedTask();
      loadSettings();
    });

    return {
      activeTab,
      tabs,
      showAddModal,
      showSortModal,
      showProjectModal,
      showSettingsModal,
      showStatisticsModal,
      projects,
      filteredProjects,
      selectedProject,
      addProjectError,
      currentTimerTask,
      lastExecutedTask,
      timerStartTime,
      pomodoroSettings,
      addProject,
      openProject,
      editProject,
      completeProject,
      deleteProject,
      updateProject,
      saveProjectOrder,
      startTimerForTask,
      handleTimerComplete,
      handleTimerStart,
      handleTimerStop,
      handleTimerMinimize,
      handleTimerMaximize,
      handleSettingsSave,
      saveFocusTime,
    };
  },
};
</script>

<style scoped>
/* Use exact same styles as habit tracker */
.pomodoro-view {
  min-height: 100vh;
  background: #212121;
  color: white;
}

/* COMPLETE REPLACEMENT - Remove ALL existing button styles and replace with this: */

.nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
  height: 40px; /* Force container height */
  margin-top: 30px;
}

/* Reset and unify ALL navigation buttons */
.nav-buttons button {
  background: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 4px;
  transition: background 0.2s;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 60px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  max-width: 40px !important;
  max-height: 40px !important;
  box-sizing: border-box !important;
  font-size: 20px !important;
  line-height: 1 !important;
}

/* Hover effect for all buttons */  
.nav-buttons button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #16d62c !important;
}

/* Icon sizing for SVG buttons */
.nav-buttons button svg {
  width: 20px !important;
  height: 20px !important;
  flex-shrink: 0;
}


.main-content {
  padding: 40px;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
