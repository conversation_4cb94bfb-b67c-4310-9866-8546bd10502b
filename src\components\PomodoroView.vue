<template>
  <div class="pomodoro-view">
    <!-- Navigation Bar - Exact same as habit tracker -->
    <nav class="nav-container">
      <div
        class="logo"
        @click="$emit('back-to-habits')"
        style="cursor: pointer"
      >
        <div class="logo-icon"></div>
        <span style="margin-bottom: 7px">ANEW</span>
      </div>

      <div class="nav-tabs">
        <div
          v-for="tab in tabs"
          :key="tab"
          class="nav-tab"
          :class="{ active: activeTab === tab }"
          @click="activeTab = tab"
        >
          {{ tab }}
        </div>
      </div>

      <div class="nav-buttons">
        <button
          class="sort-button"
          @click="showSortModal = true"
          title="Sort Projects"
        >
          <svg
            class="sort-icon"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 7L12 3L16 7"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 3V15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16 17L12 21L8 17"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 21V9"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <button class="add-button" @click="showAddModal = true">+</button>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <div class="projects-grid">
        <ProjectCard
          v-for="project in filteredProjects"
          :key="project.id"
          :project="project"
          @open-project="openProject"
          @edit-project="editProject"
          @complete-project="completeProject"
          @delete-project="deleteProject"
        />
      </div>
    </main>

    <!-- Modals -->
    <AddProjectModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @add-project="addProject"
      :error-message="addProjectError"
      :default-category="activeTab"
    />

    <ProjectViewModal
      v-if="showProjectModal"
      :project="selectedProject"
      @close="showProjectModal = false"
      @update-project="updateProject"
      @start-timer="startTimerForTask"
    />

    <SortProjectsModal
      v-if="showSortModal"
      :projects="projects"
      :activeTab="activeTab"
      @close="showSortModal = false"
      @save-order="saveProjectOrder"
    />

    <!-- Pomodoro Timer -->
    <PomodoroTimer
      v-if="showTimer"
      :task="currentTimerTask"
      :initialTime="25 * 60"
      @timer-complete="handleTimerComplete"
      @timer-stop="handleTimerStop"
      @minimize="handleTimerMinimize"
      @maximize="handleTimerMaximize"
    />
  </div>
</template>

<script>
import { ref, onMounted, computed } from "vue";
import ProjectCard from "./ProjectCard.vue";
import AddProjectModal from "./AddProjectModal.vue";
import ProjectViewModal from "./ProjectViewModal.vue";
import SortProjectsModal from "./SortProjectsModal.vue";
import PomodoroTimer from "./PomodoroTimer.vue";

export default {
  name: "PomodoroView",
  components: {
    ProjectCard,
    AddProjectModal,
    ProjectViewModal,
    SortProjectsModal,
    PomodoroTimer,
  },
  emits: ["back-to-habits"],
  setup() {
    const activeTab = ref("DAILY");
    const showAddModal = ref(false);
    const showSortModal = ref(false);
    const showProjectModal = ref(false);
    const projects = ref([]);
    const selectedProject = ref(null);
    const addProjectError = ref("");

    // Timer state
    const showTimer = ref(false);
    const currentTimerTask = ref(null);

    const tabs = ["DAILY", "CODING", "COURSES", "SCHOOL", "LIFE", "COMPLETED"];

    // Computed property to filter projects by active tab
    const filteredProjects = computed(() => {
      const filtered = projects.value.filter(
        (project) => project.category === activeTab.value
      );
      const sorted = filtered.sort(
        (a, b) => (a.order_position || 0) - (b.order_position || 0)
      );

      console.log(
        `📋 Filtered projects for ${activeTab.value}:`,
        sorted.map((p) => ({
          id: p.id,
          name: p.name,
          order_position: p.order_position,
        }))
      );

      return sorted;
    });

    // Load projects from server
    const loadProjects = async () => {
      try {
        const response = await fetch("/api/projects");
        const data = await response.json();
        if (data.success) {
          console.log(
            "🔍 Loaded projects from server:",
            data.projects.map((p) => ({
              id: p.id,
              name: p.name,
              order_position: p.order_position,
            }))
          );

          // Check if we need to initialize order positions per category
          console.log(
            "🔥 FRONTEND: Starting order position initialization check"
          );
          const projectsByCategory = {};
          data.projects.forEach((project) => {
            if (!projectsByCategory[project.category]) {
              projectsByCategory[project.category] = [];
            }
            projectsByCategory[project.category].push(project);
          });

          let needsInitialization = false;
          const projectOrdersToUpdate = [];

          // Check each category separately and assign proper order positions
          Object.keys(projectsByCategory).forEach((category) => {
            const categoryProjects = projectsByCategory[category];

            // Check if this category needs initialization
            // We need to check if there are gaps in the order positions or multiple projects with the same position
            const orderPositions = categoryProjects.map(
              (p) => p.order_position || 0
            );
            const uniquePositions = [...new Set(orderPositions)];
            const hasGaps = uniquePositions.length !== categoryProjects.length;
            const hasDuplicates =
              orderPositions.length !== uniquePositions.length;
            const hasNullUndefined = categoryProjects.some(
              (p) => p.order_position === null || p.order_position === undefined
            );

            const categoryNeedsInit =
              hasNullUndefined || hasGaps || hasDuplicates;

            console.log(
              `🔍 Category ${category} needs initialization:`,
              categoryNeedsInit
            );
            console.log(
              `🔍 Category ${category} projects:`,
              categoryProjects.map((p) => ({
                id: p.id,
                name: p.name,
                order_position: p.order_position,
              }))
            );

            if (categoryNeedsInit) {
              needsInitialization = true;
              // Sort by current order_position first, then by ID for consistency
              categoryProjects.sort((a, b) => {
                if (a.order_position !== b.order_position) {
                  return (a.order_position || 0) - (b.order_position || 0);
                }
                return a.id - b.id;
              });

              // Assign sequential order positions within this category
              categoryProjects.forEach((project, index) => {
                projectOrdersToUpdate.push({
                  id: project.id,
                  order_position: index,
                });
                project.order_position = index; // Update immediately for UI
              });
            }
          });

          if (needsInitialization && projectOrdersToUpdate.length > 0) {
            console.log(
              "🔧 Initializing order positions for projects:",
              projectOrdersToUpdate
            );

            try {
              const response = await fetch("/api/projects/reorder", {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  projectOrders: projectOrdersToUpdate,
                }),
              });

              const result = await response.json();
              if (result.success) {
                console.log("✅ Order positions initialized successfully");
              } else {
                console.error(
                  "❌ Failed to initialize order positions:",
                  result.error
                );
              }
            } catch (error) {
              console.error("❌ Error initializing order positions:", error);
            }
          }

          // Ensure all projects have order_position values
          const projectsWithOrder = data.projects.map((project, index) => ({
            ...project,
            order_position:
              project.order_position !== undefined &&
              project.order_position !== null
                ? project.order_position
                : index,
          }));

          projects.value = projectsWithOrder;
          console.log(
            "Projects with order assigned:",
            projectsWithOrder.map((p) => ({
              id: p.id,
              name: p.name,
              order_position: p.order_position,
            }))
          );
        }
      } catch (error) {
        console.error("Error loading projects:", error);
      }
    };

    // Add new project
    const addProject = async (projectData) => {
      addProjectError.value = "";
      try {
        const response = await fetch("/api/projects", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(projectData),
        });
        const data = await response.json();
        if (data.success) {
          projects.value.push(data.project);
          showAddModal.value = false;
        } else {
          addProjectError.value = data.error || "Failed to add project";
        }
      } catch (error) {
        console.error("Error adding project:", error);
        addProjectError.value = "Failed to add project. Please try again.";
      }
    };

    // Open project view
    const openProject = (project) => {
      selectedProject.value = project;
      showProjectModal.value = true;
    };

    // Edit project
    const editProject = async (projectData) => {
      try {
        const response = await fetch(`/api/projects/${projectData.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(projectData),
        });
        const data = await response.json();
        if (data.success) {
          const projectIndex = projects.value.findIndex(
            (p) => p.id === projectData.id
          );
          if (projectIndex !== -1) {
            projects.value[projectIndex] = {
              ...projects.value[projectIndex],
              ...data.project,
            };
          }
        }
      } catch (error) {
        console.error("Error editing project:", error);
      }
    };

    // Complete project (move to COMPLETED category)
    const completeProject = async (projectId) => {
      try {
        const response = await fetch(`/api/projects/${projectId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ category: "COMPLETED" }),
        });
        const data = await response.json();
        if (data.success) {
          const projectIndex = projects.value.findIndex(
            (p) => p.id === projectId
          );
          if (projectIndex !== -1) {
            projects.value[projectIndex].category = "COMPLETED";
          }
        }
      } catch (error) {
        console.error("Error completing project:", error);
      }
    };

    // Delete project
    const deleteProject = async (projectId) => {
      try {
        const response = await fetch(`/api/projects/${projectId}`, {
          method: "DELETE",
        });
        const data = await response.json();
        if (data.success) {
          projects.value = projects.value.filter((p) => p.id !== projectId);
        }
      } catch (error) {
        console.error("Error deleting project:", error);
      }
    };

    // Update project from modal
    const updateProject = (updatedProject) => {
      const projectIndex = projects.value.findIndex(
        (p) => p.id === updatedProject.id
      );
      if (projectIndex !== -1) {
        projects.value[projectIndex] = updatedProject;
      }
    };

    // Save project order from sort modal
    const saveProjectOrder = async (sortedProjects) => {
      console.log("💾 Saving project order from sort modal:", sortedProjects);

      try {
        // Update the projects array with new order positions
        sortedProjects.forEach((sortedProject, index) => {
          const projectIndex = projects.value.findIndex(
            (p) => p.id === sortedProject.id
          );
          if (projectIndex !== -1) {
            projects.value[projectIndex].order_position = index;
          }
        });

        // Create the order array for the server
        const projectOrders = sortedProjects.map((project, index) => ({
          id: project.id,
          order_position: index,
        }));

        console.log("🔄 Sending new order to server:", projectOrders);

        const response = await fetch("/api/projects/reorder", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            projectOrders: projectOrders,
          }),
        });

        const data = await response.json();
        if (data.success) {
          console.log("✅ Project order saved successfully");
          showSortModal.value = false;

          // Force reactivity update
          projects.value = [...projects.value];
        } else {
          console.error("❌ Failed to save project order:", data.error);
        }
      } catch (error) {
        console.error("❌ Error saving project order:", error);
      }
    };

    // Timer handler functions
    const startTimerForTask = (task) => {
      currentTimerTask.value = {
        ...task,
        project_name:
          projects.value.find((p) => p.id === task.project_id)?.name ||
          "Unknown Project",
      };
      showTimer.value = true;
    };

    const handleTimerComplete = (task) => {
      alert(`Pomodoro completed for: ${task?.name}`);
      showTimer.value = false;
      currentTimerTask.value = null;
    };

    const handleTimerStop = () => {
      showTimer.value = false;
      currentTimerTask.value = null;
    };

    const handleTimerMinimize = () => {
      // Timer is now minimized, but still running
    };

    const handleTimerMaximize = () => {
      // Timer is now maximized
    };

    onMounted(() => {
      loadProjects();
    });

    return {
      activeTab,
      tabs,
      showAddModal,
      showSortModal,
      showProjectModal,
      projects,
      filteredProjects,
      selectedProject,
      addProjectError,
      showTimer,
      currentTimerTask,
      addProject,
      openProject,
      editProject,
      completeProject,
      deleteProject,
      updateProject,
      saveProjectOrder,
      startTimerForTask,
      handleTimerComplete,
      handleTimerStop,
      handleTimerMinimize,
      handleTimerMaximize,
    };
  },
};
</script>

<style scoped>
/* Use exact same styles as habit tracker */
.pomodoro-view {
  min-height: 100vh;
  background: #212121;
  color: white;
}

/* Navigation styles are imported from global style.css */

.nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.sort-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sort-icon {
  width: 20px;
  height: 20px;
}

.add-button {
  background: #16d62c;
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-button:hover {
  background: #14c028;
}

.main-content {
  padding: 40px;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
