<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Report</h2>
        <button class="close-button" @click="$emit('close')">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="stats-content">
        <!-- Focus Time Stats -->
        <div class="focus-time-stats">
          <div class="stat-card">
            <div class="stat-label">Total Focus Time</div>
            <div class="stat-value total">
              {{ formatTime(stats.totalTime) }}
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Focus Time for This Year</div>
            <div class="stat-value year">{{ formatTime(stats.yearTime) }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Focus Time for This Month</div>
            <div class="stat-value month">
              {{ formatTime(stats.monthTime) }}
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Focus Time for This Week</div>
            <div class="stat-value week">{{ formatTime(stats.weekTime) }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Focus Time for Today</div>
            <div class="stat-value today">
              {{ formatTime(stats.todayTime) }}
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
          <!-- Project Time Distribution -->
          <div class="chart-container">
            <div class="chart-header">
              <h3>Project Time Distribution</h3>
              <div class="time-filter">
                <button
                  v-for="filter in timeFilters"
                  :key="filter"
                  :class="{ active: activeTimeFilter === filter }"
                  @click="activeTimeFilter = filter"
                  class="filter-btn"
                >
                  {{ filter }}
                </button>
                <div class="date-nav">
                  <button @click="navigateDate(-1)" class="nav-btn">‹</button>
                  <span class="current-period">{{ currentPeriod }}</span>
                  <button @click="navigateDate(1)" class="nav-btn">›</button>
                </div>
              </div>
            </div>
            <div class="chart-content">
              <div class="donut-chart">
                <svg viewBox="0 0 500 300" class="chart-svg">
                  <!-- Donut segments -->
                  <circle
                    v-for="(segment, index) in chartSegments"
                    :key="`segment-${index}`"
                    cx="250"
                    cy="150"
                    r="70"
                    fill="none"
                    :stroke="segment.color"
                    :stroke-width="20"
                    :stroke-dasharray="`${segment.length} ${
                      circumference - segment.length
                    }`"
                    :stroke-dashoffset="segment.offset"
                    :transform="`rotate(-90 250 150)`"
                  />

                  <!-- Line labels -->
                  <g
                    v-for="(segment, index) in chartSegments"
                    :key="`label-${index}`"
                  >
                    <line
                      :x1="segment.labelLine.x1"
                      :y1="segment.labelLine.y1"
                      :x2="segment.labelLine.x2"
                      :y2="segment.labelLine.y2"
                      stroke="#ccc"
                      stroke-width="1"
                    />
                    <line
                      :x1="segment.labelLine.x2"
                      :y1="segment.labelLine.y2"
                      :x2="segment.labelLine.x3"
                      :y2="segment.labelLine.y2"
                      stroke="#ccc"
                      stroke-width="1"
                    />
                    <text
                      :x="segment.labelLine.textX"
                      :y="segment.labelLine.textY"
                      :text-anchor="segment.labelLine.anchor"
                      class="chart-label"
                      :fill="segment.color"
                    >
                      {{ segment.project.name }}
                    </text>
                  </g>

                  <!-- Center text -->
                  <text
                    x="250"
                    y="145"
                    text-anchor="middle"
                    class="chart-center-value"
                  >
                    {{ totalProjectTime }}
                  </text>
                  <text
                    x="250"
                    y="160"
                    text-anchor="middle"
                    class="chart-center-label"
                  >
                    Total
                  </text>
                </svg>
              </div>

              <!-- Projects Legend -->
              <div class="projects-legend">
                <div
                  v-for="project in topProjects"
                  :key="project.name"
                  class="legend-item"
                >
                  <div
                    class="legend-color"
                    :style="{ backgroundColor: project.color }"
                  ></div>
                  <div class="legend-info">
                    <div class="legend-name">{{ project.name }}</div>
                    <div class="legend-time">
                      {{ formatTime(project.time) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Task Time Distribution -->
          <div class="chart-container">
            <div class="chart-header">
              <h3>Task Time Distribution</h3>
              <div class="chart-subtitle">
                Total Focus Time:
                {{
                  formatTime(topTasks.reduce((sum, task) => sum + task.time, 0))
                }}
              </div>
              <div class="time-filter">
                <button
                  v-for="filter in taskTimeFilters"
                  :key="filter"
                  :class="{ active: activeTaskFilter === filter }"
                  @click="activeTaskFilter = filter"
                  class="filter-btn"
                >
                  {{ filter }}
                </button>
                <div class="date-nav">
                  <button @click="navigateTaskDate(-1)" class="nav-btn">
                    ‹
                  </button>
                  <span class="current-period">{{ currentTaskPeriod }}</span>
                  <button @click="navigateTaskDate(1)" class="nav-btn">
                    ›
                  </button>
                </div>
              </div>
            </div>
            <div class="horizontal-bar-chart">
              <div v-for="task in topTasks" :key="task.name" class="bar-item">
                <div class="bar-label">
                  <span class="task-name">{{ task.name }}</span>
                  <span class="task-time">{{ formatTime(task.time) }}</span>
                </div>
                <div class="bar-container">
                  <div
                    class="bar-fill"
                    :style="{
                      width: `${(task.time / maxTaskTime) * 100}%`,
                      backgroundColor: task.projectColor || task.color,
                    }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Focus Time Chart and Goal Section -->
        <div class="focus-time-section">
          <div class="chart-container focus-chart">
            <div class="chart-header">
              <h3>Focus Time Chart</h3>
              <div class="chart-stats">
                <span class="stat-item"
                  >Top: {{ formatTime(focusChartStats.top) }}</span
                >
                <span class="stat-item"
                  >Average: {{ formatTime(focusChartStats.average) }}</span
                >
              </div>
              <div class="time-filter">
                <button
                  v-for="filter in focusTimeFilters"
                  :key="filter"
                  :class="{ active: activeFocusFilter === filter }"
                  @click="activeFocusFilter = filter"
                  class="filter-btn"
                >
                  {{ filter }}
                </button>
                <div class="date-nav">
                  <button @click="navigateFocusDate(-1)" class="nav-btn">
                    ‹
                  </button>
                  <span class="current-period">{{ currentFocusPeriod }}</span>
                  <button @click="navigateFocusDate(1)" class="nav-btn">
                    ›
                  </button>
                </div>
              </div>
            </div>
            <div class="stacked-bar-chart">
              <div
                id="focusChartContainer"
                style="height: 300px; width: 100%"
              ></div>
            </div>
          </div>

          <!-- Focus Time Goal -->
          <div class="chart-container focus-goal">
            <div class="chart-header">
              <h3>Focus Time Goal</h3>
              <div class="goal-info">
                <div class="goal-input-container">
                  <label class="goal-label">Daily Goal:</label>
                  <input
                    type="number"
                    v-model.number="dailyGoal"
                    min="1"
                    max="24"
                    step="0.5"
                    class="goal-input"
                  />
                  <span class="goal-unit">hours</span>
                </div>
              </div>
              <div class="date-nav">
                <button @click="navigateGoalDate(-1)" class="nav-btn">‹</button>
                <span class="current-month">{{ currentGoalMonth }}</span>
                <button @click="navigateGoalDate(1)" class="nav-btn">›</button>
              </div>
            </div>
            <div class="calendar-view">
              <div class="calendar-header">
                <div class="day-header" v-for="day in dayHeaders" :key="day">
                  {{ day }}
                </div>
              </div>
              <div class="calendar-grid">
                <div
                  v-for="date in calendarDates"
                  :key="date.key"
                  class="calendar-date"
                  :class="{
                    'other-month': !date.isCurrentMonth,
                    today: date.isToday,
                  }"
                >
                  <div class="date-number">{{ date.day }}</div>
                  <div
                    v-if="date.isCurrentMonth && date.focusTime > 0"
                    class="progress-circle"
                    :style="{
                      background: `conic-gradient(#ff4444 0deg ${date.progressAngle}deg, transparent ${date.progressAngle}deg 360deg)`,
                    }"
                  >
                    <div class="circle-inner"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from "vue";

export default {
  name: "StatisticsModal",
  emits: ["close"],
  setup() {
    const activeTimeFilter = ref("Monthly");
    const activeTaskFilter = ref("Daily");
    const activeFocusFilter = ref("Daily");
    const currentDate = ref(new Date());
    const currentTaskDate = ref(new Date());
    const currentFocusDate = ref(new Date());
    const currentGoalDate = ref(new Date());

    const timeFilters = ["Daily", "Weekly", "Monthly", "Yearly"];
    const taskTimeFilters = ["Daily", "Weekly", "Monthly", "Yearly"];
    const focusTimeFilters = ["Daily", "Weekly", "Monthly", "Yearly"];

    // Goal settings
    const dailyGoal = ref(8); // 8 hours default goal

    // Load goal from localStorage
    const loadGoalSettings = () => {
      try {
        const savedGoal = localStorage.getItem("dailyFocusGoal");
        if (savedGoal) {
          dailyGoal.value = parseFloat(savedGoal);
        }
      } catch (error) {
        console.error("Error loading goal settings:", error);
      }
    };

    // Save goal to localStorage
    const saveGoalSettings = () => {
      try {
        localStorage.setItem("dailyFocusGoal", dailyGoal.value.toString());
      } catch (error) {
        console.error("Error saving goal settings:", error);
      }
    };

    // Watch for goal changes and save
    watch(dailyGoal, () => {
      saveGoalSettings();
    });

    // Tooltip state
    const tooltip = ref({
      show: false,
      x: 0,
      y: 0,
      content: "",
    });

    // Calendar constants
    const dayHeaders = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];

    const stats = ref({
      totalTime: 0,
      yearTime: 0,
      monthTime: 0,
      weekTime: 0,
      todayTime: 0,
    });

    const projectStats = ref([]);
    const taskStats = ref([]);

    // Chart colors
    const colors = [
      "#FFD700",
      "#FF6B35",
      "#4ECDC4",
      "#45B7D1",
      "#96CEB4",
      "#FFEAA7",
      "#DDA0DD",
      "#98D8C8",
    ];

    // Load statistics from localStorage
    const loadStatistics = () => {
      try {
        const focusData = JSON.parse(
          localStorage.getItem("focusTimeData") || "[]"
        );

        const now = new Date();
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        const startOfDay = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate()
        );

        // Calculate time periods
        stats.value.totalTime = focusData.reduce(
          (sum, entry) => sum + entry.duration,
          0
        );
        stats.value.yearTime = focusData
          .filter((entry) => new Date(entry.date) >= startOfYear)
          .reduce((sum, entry) => sum + entry.duration, 0);
        stats.value.monthTime = focusData
          .filter((entry) => new Date(entry.date) >= startOfMonth)
          .reduce((sum, entry) => sum + entry.duration, 0);
        stats.value.weekTime = focusData
          .filter((entry) => new Date(entry.date) >= startOfWeek)
          .reduce((sum, entry) => sum + entry.duration, 0);
        stats.value.todayTime = focusData
          .filter((entry) => new Date(entry.date) >= startOfDay)
          .reduce((sum, entry) => sum + entry.duration, 0);

        // Calculate project stats
        const projectTimes = {};
        focusData.forEach((entry) => {
          if (!projectTimes[entry.projectName]) {
            projectTimes[entry.projectName] = 0;
          }
          projectTimes[entry.projectName] += entry.duration;
        });

        projectStats.value = Object.entries(projectTimes)
          .map(([name, time], index) => ({
            name,
            time,
            color: colors[index % colors.length],
          }))
          .sort((a, b) => b.time - a.time);

        // Calculate task stats with project association
        const taskTimes = {};
        const taskProjects = {};
        focusData.forEach((entry) => {
          if (!taskTimes[entry.taskName]) {
            taskTimes[entry.taskName] = 0;
            taskProjects[entry.taskName] = entry.projectName;
          }
          taskTimes[entry.taskName] += entry.duration;
        });

        taskStats.value = Object.entries(taskTimes)
          .map(([name, time], index) => {
            const projectName = taskProjects[name];
            const projectColor =
              projectStats.value.find((p) => p.name === projectName)?.color ||
              colors[index % colors.length];
            return {
              name,
              time,
              color: colors[index % colors.length],
              projectColor: projectColor,
              projectName: projectName,
            };
          })
          .sort((a, b) => b.time - a.time);
      } catch (error) {
        console.error("Error loading statistics:", error);
      }
    };

    // Format time in hours and minutes
    const formatTime = (seconds) => {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);

      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      } else {
        return `${minutes}m`;
      }
    };

    // Chart calculations
    const circumference = 2 * Math.PI * 70;

    const topProjects = computed(() => {
      try {
        const focusData = JSON.parse(
          localStorage.getItem("focusTimeData") || "[]"
        );
        const now = currentDate.value;
        let startDate, endDate;

        switch (activeTimeFilter.value) {
          case "Daily":
            startDate = new Date(
              now.getFullYear(),
              now.getMonth(),
              now.getDate()
            );
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 1);
            break;
          case "Weekly":
            startDate = new Date(now);
            startDate.setDate(now.getDate() - now.getDay());
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 7);
            break;
          case "Monthly":
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            break;
          case "Yearly":
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear() + 1, 0, 1);
            break;
          default:
            return projectStats.value.slice(0, 6);
        }

        // Filter data for the selected period
        const periodData = focusData.filter((entry) => {
          const entryDate = new Date(entry.date);
          return entryDate >= startDate && entryDate < endDate;
        });

        // Calculate project times for this period
        const projectTimes = {};
        periodData.forEach((entry) => {
          if (!projectTimes[entry.projectName]) {
            projectTimes[entry.projectName] = 0;
          }
          projectTimes[entry.projectName] += entry.duration;
        });

        const periodProjects = Object.entries(projectTimes)
          .map(([name, time]) => {
            // Find the main project color from projectStats
            const mainProject = projectStats.value.find((p) => p.name === name);
            return {
              name,
              time,
              color:
                mainProject?.color ||
                colors[Object.keys(projectTimes).indexOf(name) % colors.length],
            };
          })
          .sort((a, b) => b.time - a.time);

        return periodProjects.slice(0, 6);
      } catch (error) {
        console.error("Error calculating period projects:", error);
        return projectStats.value.slice(0, 6);
      }
    });

    const topTasks = computed(() => {
      try {
        const focusData = JSON.parse(
          localStorage.getItem("focusTimeData") || "[]"
        );
        const now = currentTaskDate.value;
        let startDate, endDate;

        switch (activeTaskFilter.value) {
          case "Daily":
            startDate = new Date(
              now.getFullYear(),
              now.getMonth(),
              now.getDate()
            );
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 1);
            break;
          case "Weekly":
            startDate = new Date(now);
            startDate.setDate(now.getDate() - now.getDay());
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 7);
            break;
          case "Monthly":
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            break;
          case "Yearly":
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear() + 1, 0, 1);
            break;
          default:
            return taskStats.value.slice(0, 8);
        }

        // Filter data for the selected period
        const periodData = focusData.filter((entry) => {
          const entryDate = new Date(entry.date);
          return entryDate >= startDate && entryDate < endDate;
        });

        // Calculate task times for this period with project association
        const taskTimes = {};
        const taskProjects = {};
        periodData.forEach((entry) => {
          if (!taskTimes[entry.taskName]) {
            taskTimes[entry.taskName] = 0;
            taskProjects[entry.taskName] = entry.projectName;
          }
          taskTimes[entry.taskName] += entry.duration;
        });

        const periodTasks = Object.entries(taskTimes)
          .map(([name, time]) => {
            const projectName = taskProjects[name];
            const projectColor =
              projectStats.value.find((p) => p.name === projectName)?.color ||
              colors[Object.keys(taskTimes).indexOf(name) % colors.length];
            return {
              name,
              time,
              color: projectColor, // Use project color as main color
              projectColor: projectColor,
              projectName: projectName,
            };
          })
          .sort((a, b) => b.time - a.time);

        return periodTasks.slice(0, 8);
      } catch (error) {
        console.error("Error calculating period tasks:", error);
        return taskStats.value.slice(0, 8);
      }
    });

    const maxTaskTime = computed(() => {
      return Math.max(...topTasks.value.map((task) => task.time), 1);
    });

    const totalProjectTime = computed(() => {
      const total = topProjects.value.reduce(
        (sum, project) => sum + project.time,
        0
      );
      return formatTime(total);
    });

    const chartSegments = computed(() => {
      const total = topProjects.value.reduce(
        (sum, project) => sum + project.time,
        0
      );
      if (total === 0) return [];

      let currentOffset = 0;
      return topProjects.value.map((project) => {
        const percentage = project.time / total;
        const length = percentage * circumference;

        // Calculate the middle angle of this segment for label positioning
        const startAngle = (currentOffset / circumference) * 2 * Math.PI;
        const endAngle =
          ((currentOffset + length) / circumference) * 2 * Math.PI;
        const midAngle = (startAngle + endAngle) / 2;

        // Calculate label line positions with adjusted center (250, 150)
        const innerRadius = 90;
        const outerRadius = 120;
        const x1 = 250 + Math.cos(midAngle) * innerRadius;
        const y1 = 150 + Math.sin(midAngle) * innerRadius;
        const x2 = 250 + Math.cos(midAngle) * outerRadius;
        const y2 = 150 + Math.sin(midAngle) * outerRadius;

        // Determine if label should be on left or right side
        const isRightSide = Math.cos(midAngle) > 0;
        const x3 = x2 + (isRightSide ? 20 : -20);

        const segment = {
          length,
          offset: -currentOffset,
          color: project.color,
          project: project,
          labelLine: {
            x1: x1,
            y1: y1,
            x2: x2,
            y2: y2,
            x3: x3,
            textX: x3 + (isRightSide ? 5 : -5),
            textY: y2 + 4,
            anchor: isRightSide ? "start" : "end",
          },
        };
        currentOffset += length;
        return segment;
      });
    });

    const currentPeriod = computed(() => {
      const date = currentDate.value;
      switch (activeTimeFilter.value) {
        case "Daily":
          return date.toLocaleDateString();
        case "Weekly":
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          return `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
        case "Monthly":
          return date.toLocaleDateString("en-US", {
            month: "long",
            year: "numeric",
          });
        case "Yearly":
          return date.getFullYear().toString();
        default:
          return "";
      }
    });

    const currentTaskPeriod = computed(() => {
      const date = currentTaskDate.value;
      switch (activeTaskFilter.value) {
        case "Daily":
          return date.toLocaleDateString();
        case "Weekly":
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          return `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
        case "Monthly":
          return date.toLocaleDateString("en-US", {
            month: "long",
            year: "numeric",
          });
        case "Yearly":
          return date.getFullYear().toString();
        default:
          return "";
      }
    });

    const navigateDate = (direction) => {
      const newDate = new Date(currentDate.value);
      switch (activeTimeFilter.value) {
        case "Daily":
          newDate.setDate(newDate.getDate() + direction);
          break;
        case "Weekly":
          newDate.setDate(newDate.getDate() + direction * 7);
          break;
        case "Monthly":
          newDate.setMonth(newDate.getMonth() + direction);
          break;
        case "Yearly":
          newDate.setFullYear(newDate.getFullYear() + direction);
          break;
      }
      currentDate.value = newDate;
    };

    const navigateTaskDate = (direction) => {
      const newDate = new Date(currentTaskDate.value);
      switch (activeTaskFilter.value) {
        case "Daily":
          newDate.setDate(newDate.getDate() + direction);
          break;
        case "Weekly":
          newDate.setDate(newDate.getDate() + direction * 7);
          break;
        case "Monthly":
          newDate.setMonth(newDate.getMonth() + direction);
          break;
        case "Yearly":
          newDate.setFullYear(newDate.getFullYear() + direction);
          break;
      }
      currentTaskDate.value = newDate;
    };

    // Focus chart navigation
    const navigateFocusDate = (direction) => {
      const newDate = new Date(currentFocusDate.value);
      switch (activeFocusFilter.value) {
        case "Daily":
          newDate.setDate(newDate.getDate() + direction);
          break;
        case "Weekly":
          newDate.setDate(newDate.getDate() + direction * 7);
          break;
        case "Monthly":
          newDate.setMonth(newDate.getMonth() + direction);
          break;
        case "Yearly":
          newDate.setFullYear(newDate.getFullYear() + direction);
          break;
      }
      currentFocusDate.value = newDate;
    };

    // Goal calendar navigation
    const navigateGoalDate = (direction) => {
      const newDate = new Date(currentGoalDate.value);
      newDate.setMonth(newDate.getMonth() + direction);
      currentGoalDate.value = newDate;
    };

    // Focus chart computed properties
    const currentFocusPeriod = computed(() => {
      const date = currentFocusDate.value;
      switch (activeFocusFilter.value) {
        case "Daily":
          return date.toLocaleDateString();
        case "Weekly":
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          return `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
        case "Monthly":
          return date.toLocaleDateString("en-US", {
            month: "long",
            year: "numeric",
          });
        case "Yearly":
          return date.getFullYear().toString();
        default:
          return "";
      }
    });

    const currentGoalMonth = computed(() => {
      return currentGoalDate.value.toLocaleDateString("en-US", {
        month: "long",
        year: "numeric",
      });
    });

    const yAxisMetrics = computed(() => {
      switch (activeFocusFilter.value) {
        case "Daily":
          return [0, 3, 6, 9, 12];
        case "Weekly":
          return [0, 19, 38, 57];
        case "Monthly":
          return [0, 65, 128, 256];
        case "Yearly":
          return [0, 256, 512, 1024];
        default:
          return [0, 3, 6, 9, 12];
      }
    });

    const maxYValue = computed(() => {
      const metrics = yAxisMetrics.value;
      return metrics[metrics.length - 1];
    });

    // Chart instance reference
    let focusChart = null;

    const renderFocusChart = () => {
      try {
        const focusData = JSON.parse(
          localStorage.getItem("focusTimeData") || "[]"
        );
        const barCount =
          {
            Daily: 30,
            Weekly: 15,
            Monthly: 12,
            Yearly: 10,
          }[activeFocusFilter.value] || 30;

        const now = new Date(currentFocusDate.value);
        const projectDataSeries = {};

        // Initialize project data series using main project colors
        projectStats.value.forEach((project) => {
          projectDataSeries[project.name] = {
            type: "stackedColumn",
            name: project.name,
            showInLegend: true,
            color: project.color, // Use main project color
            dataPoints: [],
          };
        });

        // Generate data points for each time period
        for (let i = barCount - 1; i >= 0; i--) {
          let startDate, endDate, xValue;

          switch (activeFocusFilter.value) {
            case "Daily":
              startDate = new Date(now);
              startDate.setDate(now.getDate() - i);
              endDate = new Date(startDate);
              endDate.setDate(startDate.getDate() + 1);
              xValue = new Date(startDate);
              break;
            case "Weekly":
              startDate = new Date(now);
              startDate.setDate(now.getDate() - i * 7);
              startDate.setDate(startDate.getDate() - startDate.getDay());
              endDate = new Date(startDate);
              endDate.setDate(startDate.getDate() + 7);
              xValue = new Date(startDate);
              break;
            case "Monthly":
              startDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
              endDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
              xValue = new Date(startDate);
              break;
            case "Yearly":
              startDate = new Date(now.getFullYear() - i, 0, 1);
              endDate = new Date(now.getFullYear() - i + 1, 0, 1);
              xValue = new Date(startDate);
              break;
          }

          // Filter data for this time period
          const periodData = focusData.filter((entry) => {
            const entryDate = new Date(entry.date);
            return entryDate >= startDate && entryDate < endDate;
          });

          // Group by project and calculate hours
          const projectHours = {};
          periodData.forEach((entry) => {
            if (!projectHours[entry.projectName]) {
              projectHours[entry.projectName] = 0;
            }
            projectHours[entry.projectName] += entry.duration / 3600;
          });

          // Add data points for each project
          projectStats.value.forEach((project) => {
            const hours = projectHours[project.name] || 0;
            projectDataSeries[project.name].dataPoints.push({
              x: xValue,
              y: hours,
            });
          });
        }

        // Convert to array format for CanvasJS
        const dataSeries = Object.values(projectDataSeries);

        // Create or update chart
        if (focusChart) {
          focusChart.destroy();
        }

        focusChart = new CanvasJS.Chart("focusChartContainer", {
          animationEnabled: true,
          theme: "dark2",
          backgroundColor: "#333",
          title: {
            text: "",
            fontColor: "#fff",
          },
          axisX: {
            valueFormatString:
              activeFocusFilter.value === "Daily"
                ? "DD"
                : activeFocusFilter.value === "Weekly"
                ? "MMM DD"
                : activeFocusFilter.value === "Monthly"
                ? "MMM"
                : "YYYY",
            labelFontColor: "#ccc",
            lineColor: "#555",
            tickColor: "#555",
          },
          axisY: {
            suffix: "h",
            maximum: maxYValue.value,
            labelFontColor: "#ccc",
            lineColor: "#555",
            tickColor: "#555",
            gridColor: "#555",
          },
          toolTip: {
            shared: true,
            backgroundColor: "rgba(0,0,0,0.9)",
            fontColor: "#fff",
            borderColor: "#666",
          },
          legend: {
            fontColor: "#ccc",
            verticalAlign: "bottom",
            horizontalAlign: "center",
          },
          data: dataSeries,
        });

        focusChart.render();
      } catch (error) {
        console.error("Error rendering focus chart:", error);
      }
    };

    const focusChartData = computed(() => {
      // This is kept for compatibility but chart rendering is handled by renderFocusChart
      return [];
    });

    const focusChartStats = computed(() => {
      try {
        const focusData = JSON.parse(
          localStorage.getItem("focusTimeData") || "[]"
        );
        const barCount =
          {
            Daily: 30,
            Weekly: 15,
            Monthly: 12,
            Yearly: 10,
          }[activeFocusFilter.value] || 30;

        const now = new Date(currentFocusDate.value);
        const totals = [];

        for (let i = barCount - 1; i >= 0; i--) {
          let startDate, endDate;

          switch (activeFocusFilter.value) {
            case "Daily":
              startDate = new Date(now);
              startDate.setDate(now.getDate() - i);
              endDate = new Date(startDate);
              endDate.setDate(startDate.getDate() + 1);
              break;
            case "Weekly":
              startDate = new Date(now);
              startDate.setDate(now.getDate() - i * 7);
              startDate.setDate(startDate.getDate() - startDate.getDay());
              endDate = new Date(startDate);
              endDate.setDate(startDate.getDate() + 7);
              break;
            case "Monthly":
              startDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
              endDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
              break;
            case "Yearly":
              startDate = new Date(now.getFullYear() - i, 0, 1);
              endDate = new Date(now.getFullYear() - i + 1, 0, 1);
              break;
          }

          const periodData = focusData.filter((entry) => {
            const entryDate = new Date(entry.date);
            return entryDate >= startDate && entryDate < endDate;
          });

          const totalSeconds = periodData.reduce(
            (sum, entry) => sum + entry.duration,
            0
          );
          totals.push(totalSeconds);
        }

        if (totals.length === 0) return { top: 0, average: 0 };

        const top = Math.max(...totals);
        const average =
          totals.reduce((sum, total) => sum + total, 0) / totals.length;

        return { top, average };
      } catch (error) {
        console.error("Error calculating focus chart stats:", error);
        return { top: 0, average: 0 };
      }
    });

    const calendarDates = computed(() => {
      const year = currentGoalDate.value.getFullYear();
      const month = currentGoalDate.value.getMonth();
      const today = new Date();

      // Get first day of month and calculate starting date
      const firstDay = new Date(year, month, 1);
      const startDate = new Date(firstDay);
      const dayOfWeek = (firstDay.getDay() + 6) % 7; // Convert Sunday=0 to Monday=0
      startDate.setDate(startDate.getDate() - dayOfWeek);

      // Get focus data for this month
      const focusData = JSON.parse(
        localStorage.getItem("focusTimeData") || "[]"
      );
      const monthStart = new Date(year, month, 1);
      const monthEnd = new Date(year, month + 1, 1);

      const monthFocusData = focusData.filter((entry) => {
        const entryDate = new Date(entry.date);
        return entryDate >= monthStart && entryDate < monthEnd;
      });

      // Group by day
      const dailyFocus = {};
      monthFocusData.forEach((entry) => {
        const day = new Date(entry.date).getDate();
        if (!dailyFocus[day]) {
          dailyFocus[day] = 0;
        }
        dailyFocus[day] += entry.duration;
      });

      const dates = [];
      const current = new Date(startDate);

      // Generate 42 days (6 weeks)
      for (let i = 0; i < 42; i++) {
        const isCurrentMonth = current.getMonth() === month;
        const isToday = current.toDateString() === today.toDateString();
        const day = current.getDate();
        const focusTime = isCurrentMonth ? dailyFocus[day] || 0 : 0;
        const progressAngle = Math.min(
          (focusTime / (dailyGoal.value * 3600)) * 360,
          360
        );

        dates.push({
          key: current.toISOString(),
          day: day,
          isCurrentMonth: isCurrentMonth,
          isToday: isToday,
          focusTime: focusTime,
          progressAngle: progressAngle,
        });

        current.setDate(current.getDate() + 1);
      }

      return dates;
    });

    // Tooltip functions
    const showTooltip = (event, bar) => {
      tooltip.value = {
        show: true,
        x: event.clientX,
        y: event.clientY,
        content: `${formatTime(bar.totalHours * 3600)} - ${bar.dateRange}`,
      };
    };

    const hideTooltip = () => {
      tooltip.value.show = false;
    };

    // Watch for changes to re-render chart
    watch(
      [activeFocusFilter, currentFocusDate, projectStats],
      () => {
        if (typeof CanvasJS !== "undefined") {
          setTimeout(() => renderFocusChart(), 100);
        }
      },
      { deep: true }
    );

    onMounted(() => {
      loadStatistics();
      loadGoalSettings();

      // Load CanvasJS and render chart
      const script = document.createElement("script");
      script.src = "https://cdn.canvasjs.com/canvasjs.min.js";
      script.onload = () => {
        setTimeout(() => renderFocusChart(), 200);
      };
      document.head.appendChild(script);
    });

    return {
      stats,
      activeTimeFilter,
      activeTaskFilter,
      activeFocusFilter,
      timeFilters,
      taskTimeFilters,
      focusTimeFilters,
      currentPeriod,
      currentTaskPeriod,
      currentFocusPeriod,
      currentGoalMonth,
      topProjects,
      topTasks,
      maxTaskTime,
      chartSegments,
      circumference,
      totalProjectTime,
      yAxisMetrics,
      maxYValue,
      focusChartData,
      focusChartStats,
      calendarDates,
      dayHeaders,
      dailyGoal,
      tooltip,
      formatTime,
      navigateDate,
      navigateTaskDate,
      navigateFocusDate,
      navigateGoalDate,
      showTooltip,
      hideTooltip,
      renderFocusChart,
    };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.modal-content {
  background: #2a2a2a;
  border-radius: 16px;
  border-width: 10px;
  border-color: green;
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  color: white;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #444;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-button svg {
  width: 20px;
  height: 20px;
}

.stats-content {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

.focus-time-stats {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: #333;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.stat-label {
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
}

.stat-value.total {
  color: #ffd700;
}
.stat-value.year {
  color: #ff6b35;
}
.stat-value.month {
  color: #4ecdc4;
}
.stat-value.week {
  color: #45b7d1;
}
.stat-value.today {
  color: #96ceb4;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 0.5fr;
  gap: 24px;
  margin-bottom: 30px;
}

.chart-container {
  background: #333;
  border-radius: 12px;
  padding: 20px;
}

.chart-header {
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.chart-subtitle {
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 12px;
}

.time-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #444;
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s;
}

.filter-btn:hover {
  background: #555;
}

.filter-btn.active {
  background: #ff4444;
}

.date-nav {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.nav-btn {
  background: #444;
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.nav-btn:hover {
  background: #555;
}

.current-period {
  font-size: 0.8rem;
  color: #ccc;
  min-width: 120px;
  text-align: center;
}

.chart-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 20px;
}

.donut-chart {
  flex-shrink: 0;
  flex: 2;
}

.chart-svg {
  width: 100%;
  height: auto;
  max-width: 450px;
  aspect-ratio: 500/300;
}

.chart-center-value {
  font-size: 18px;
  font-weight: bold;
  fill: white;
}

.chart-center-label {
  font-size: 12px;
  fill: #ccc;
}

.chart-label {
  font-size: 11px;
  font-weight: 500;
}

.projects-legend {
  background: #444;
  border-radius: 8px;
  padding: 12px;
  width: 140px;
  max-height: 280px;
  overflow-y: auto;
  flex-shrink: 0;
}

.legend-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 10px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 2px;
}

.legend-info {
  flex: 1;
  min-width: 0;
}

.legend-name {
  font-size: 0.8rem;
  color: white;
  margin-bottom: 2px;
  word-wrap: break-word;
  line-height: 1.2;
}

.legend-time {
  font-size: 0.75rem;
  color: #ccc;
}

.horizontal-bar-chart {
  max-height: 300px;
  overflow-y: auto;
}

.bar-item {
  margin-bottom: 16px;
}

.bar-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.task-name {
  font-size: 0.9rem;
  color: white;
  font-weight: 500;
}

.task-time {
  font-size: 0.8rem;
  color: #ccc;
}

.bar-container {
  width: 100%;
  height: 8px;
  background: #555;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Focus Time Section Styles */
.focus-time-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-container.focus-chart {
  background: #333;
  border-radius: 12px;
  padding: 20px;
}

.chart-container.focus-goal {
  background: #333;
  border-radius: 12px;
  padding: 20px;
}

.goal-info {
  margin-bottom: 12px;
}

.goal-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.goal-label {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
}

.goal-input {
  background: #444;
  border: 1px solid #555;
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  width: 60px;
  font-size: 0.9rem;
  text-align: center;
}

.goal-input:focus {
  outline: none;
  border-color: #ff4444;
}

.goal-unit {
  font-size: 0.9rem;
  color: #ccc;
}

/* Calendar Styles */
.calendar-view {
  margin-top: 16px;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 8px;
}

.day-header {
  text-align: center;
  font-size: 0.8rem;
  color: #ccc;
  padding: 8px 4px;
  font-weight: 500;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.calendar-date {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #444;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.calendar-date:hover {
  background: #555;
}

.calendar-date.other-month {
  background: #2a2a2a;
  color: #666;
}

.calendar-date.today {
  background: #4a4a4a;
  border: 2px solid #ff4444;
}

.date-number {
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 2;
  position: relative;
}

.progress-circle {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-inner {
  width: 60%;
  height: 60%;
  background: #444;
  border-radius: 50%;
}

@media (max-width: 1024px) {
  .focus-time-stats {
    grid-template-columns: repeat(3, 1fr);
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .chart-content {
    justify-content: center;
  }

  .projects-legend {
    width: 160px;
  }
}

@media (max-width: 768px) {
  .focus-time-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-content {
    flex-direction: column;
    align-items: center;
  }

  .projects-legend {
    width: 100%;
    max-width: 280px;
  }
}
</style>
