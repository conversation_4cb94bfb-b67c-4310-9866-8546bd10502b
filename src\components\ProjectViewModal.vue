<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <!-- Header -->
      <div class="modal-header">
        <h2>{{ project.name }}</h2>
        <button class="close-button" @click="$emit('close')">×</button>
      </div>

      <!-- Elapsed Time Display -->
      <div class="elapsed-time">
        <div class="time-display">
          <span class="hours">{{ formatHours(project.totalTime || 0) }}</span>
          <span class="unit">h</span>
          <span class="minutes">{{
            formatMinutes(project.totalTime || 0)
          }}</span>
          <span class="unit">m</span>
        </div>
        <div class="time-label">Elapsed Time</div>
      </div>

      <!-- Add Task Input -->
      <div class="add-task-section">
        <input
          type="text"
          v-model="newTaskName"
          placeholder="Add a task"
          class="add-task-input"
          @keyup.enter="addTask"
        />
      </div>

      <!-- Tasks List -->
      <div class="tasks-list">
        <div v-for="task in tasks" :key="task.id" class="task-item">
          <button class="play-button" @click="startPomodoro(task)">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z" />
            </svg>
          </button>

          <div class="task-content">
            <span class="task-name">{{ task.name }}</span>
          </div>

          <div class="task-actions">
            <button class="action-button" @click="editTask(task)">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                />
              </svg>
            </button>
            <button class="action-button delete" @click="deleteTask(task.id)">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";

export default {
  name: "ProjectViewModal",
  props: {
    project: {
      type: Object,
      required: true,
    },
  },
  emits: ["close", "update-project", "start-timer"],
  setup(props, { emit }) {
    const tasks = ref([]);
    const newTaskName = ref("");

    // Load tasks for this project
    const loadTasks = async () => {
      try {
        const response = await fetch(`/api/projects/${props.project.id}/tasks`);
        const data = await response.json();
        if (data.success) {
          tasks.value = data.tasks;
        }
      } catch (error) {
        console.error("Error loading tasks:", error);
      }
    };

    // Add new task
    const addTask = async () => {
      if (!newTaskName.value.trim()) return;

      try {
        const response = await fetch(
          `/api/projects/${props.project.id}/tasks`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name: newTaskName.value.trim(),
            }),
          }
        );
        const data = await response.json();
        if (data.success) {
          tasks.value.push(data.task);
          newTaskName.value = "";
        }
      } catch (error) {
        console.error("Error adding task:", error);
      }
    };

    // Edit task
    const editTask = async (task) => {
      const newName = prompt("Edit task name:", task.name);
      if (newName && newName.trim() !== task.name) {
        try {
          const response = await fetch(`/api/tasks/${task.id}`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name: newName.trim(),
            }),
          });
          const data = await response.json();
          if (data.success) {
            const taskIndex = tasks.value.findIndex((t) => t.id === task.id);
            if (taskIndex !== -1) {
              tasks.value[taskIndex].name = newName.trim();
            }
          }
        } catch (error) {
          console.error("Error editing task:", error);
        }
      }
    };

    // Delete task
    const deleteTask = async (taskId) => {
      if (confirm("Are you sure you want to delete this task?")) {
        try {
          const response = await fetch(`/api/tasks/${taskId}`, {
            method: "DELETE",
          });
          const data = await response.json();
          if (data.success) {
            tasks.value = tasks.value.filter((t) => t.id !== taskId);
          }
        } catch (error) {
          console.error("Error deleting task:", error);
        }
      }
    };

    // Start pomodoro timer
    const startPomodoro = (task) => {
      emit("start-timer", {
        ...task,
        project_id: props.project.id,
        project_name: props.project.name,
      });
    };

    // Formatting functions
    const formatHours = (totalMinutes) => {
      return Math.floor(totalMinutes / 60)
        .toString()
        .padStart(2, "0");
    };

    const formatMinutes = (totalMinutes) => {
      return (totalMinutes % 60).toString().padStart(2, "0");
    };

    onMounted(() => {
      loadTasks();
    });

    return {
      tasks,
      newTaskName,
      addTask,
      editTask,
      deleteTask,
      startPomodoro,
      formatHours,
      formatMinutes,
    };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a1a;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  color: white;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #333;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.elapsed-time {
  text-align: center;
  padding: 2rem;
  background: #2a2a2a;
  margin: 1.5rem;
  border-radius: 8px;
}

.time-display {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.hours,
.minutes {
  color: #e74c3c;
}

.unit {
  color: #888;
  font-size: 2rem;
}

.time-label {
  color: #888;
  font-size: 1rem;
}

.add-task-section {
  padding: 0 1.5rem;
  margin-bottom: 1rem;
}

.add-task-input {
  width: 100%;
  padding: 1rem;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
}

.add-task-input:focus {
  outline: none;
  border-color: #2d9d65;
}

.tasks-list {
  padding: 0 1.5rem 1.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2a2a2a;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.play-button {
  background: none;
  border: 2px solid #e74c3c;
  color: #e74c3c;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.play-button:hover:not(:disabled) {
  background: #e74c3c;
  color: white;
}

.play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-button svg {
  width: 16px;
  height: 16px;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 1rem;
  color: white;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.action-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.action-button.delete:hover {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.action-button svg {
  width: 16px;
  height: 16px;
}
</style>
