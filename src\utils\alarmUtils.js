// Alarm utility for playing pomodoro and break alarms
class AlarmManager {
  constructor() {
    this.alarmSounds = {
      beep: null,
      guitar: null,
    };
    this.initialized = false;
  }

  // Initialize audio files
  initializeAudio() {
    if (this.initialized) return;

    try {
      // Create audio instances with error handling
      this.alarmSounds.beep = new Audio();
      this.alarmSounds.guitar = new Audio();
      
      // Try to load the audio files
      this.alarmSounds.beep.src = "/Alarm Beep.wav";
      this.alarmSounds.guitar.src = "/Guitar.wav";
      
      // Handle loading errors gracefully
      Object.entries(this.alarmSounds).forEach(([key, audio]) => {
        if (audio) {
          audio.onerror = () => {
            console.warn(`Audio file not found: ${key}. Using fallback beep.`);
            this.createFallbackBeep(key);
          };
        }
      });

      this.initialized = true;
    } catch (error) {
      console.error("Error initializing audio:", error);
      // Create fallback beeps for both sounds
      this.createFallbackBeep('beep');
      this.createFallback<PERSON>eep('guitar');
      this.initialized = true;
    }
  }

  // Create a simple beep sound using Web Audio API
  createFallbackBeep(type) {
    try {
      const audioContext = new AudioContext();
      
      this.alarmSounds[type] = {
        play: () => {
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);
          
          // Different frequencies for different alarm types
          oscillator.frequency.setValueAtTime(
            type === 'guitar' ? 440 : 800,
            audioContext.currentTime
          );
          oscillator.type = type === 'guitar' ? 'sine' : 'square';
          
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
          
          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.5);
          
          return Promise.resolve();
        },
        volume: 0.5,
        currentTime: 0
      };
    } catch (error) {
      console.error('Error creating fallback beep:', error);
    }
  }

  // Play alarm for specific event
  playAlarm(eventType) {
    try {
      // Initialize audio if not already done
      if (!this.initialized) {
        this.initializeAudio();
      }

      // Load current settings
      const currentSettings = JSON.parse(localStorage.getItem("pomodoroSettings") || "{}");
      const alarmType = eventType === "pomodoroEnd" 
        ? (currentSettings.pomodoroAlarm || "beep")
        : (currentSettings.breakAlarm || "beep");
      const volume = (currentSettings.alarmVolume || 50) / 100;

      const audio = this.alarmSounds[alarmType];
      if (audio) {
        audio.currentTime = 0;
        if (audio.volume !== undefined) {
          audio.volume = volume;
        }
        audio.play().catch((error) => {
          console.error("Error playing automatic alarm:", error);
        });
      }
    } catch (error) {
      console.error("Error in automatic alarm:", error);
    }
  }
}

// Create singleton instance
const alarmManager = new AlarmManager();

// Export functions for use in other components
export const playPomodoroEndAlarm = () => {
  alarmManager.playAlarm("pomodoroEnd");
};

export const playBreakEndAlarm = () => {
  alarmManager.playAlarm("breakEnd");
};

export const initializeAlarms = () => {
  alarmManager.initializeAudio();
};

export default alarmManager;
