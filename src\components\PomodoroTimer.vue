<template>
  <!-- Fullscreen Timer -->
  <div v-if="!isMinimized" class="pomodoro-timer-fullscreen">
    <div class="timer-header">
      <button class="minimize-button" @click="minimizeTimer">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M7 14l5-5 5 5z" />
        </svg>
      </button>
    </div>

    <!-- Task name centered above timer -->
    <div class="project-name-centered">
      {{ currentTask?.project_name || "Pomodoro" }}
    </div>

    <div class="timer-main">
      <!-- Center content -->
      <div class="timer-center">
        <div class="timer-circle">
          <svg class="progress-ring" width="360" height="360">
            <circle
              class="progress-ring-background"
              cx="180"
              cy="180"
              r="170"
              fill="transparent"
              stroke="#444"
              stroke-width="16"
              stroke-dasharray="10 10"
            />
            <circle
              class="progress-ring-progress"
              cx="180"
              cy="180"
              r="170"
              fill="transparent"
              stroke="#ff4444"
              stroke-width="16"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="strokeDashoffset"
              stroke-dasharray="10 10"
              stroke-linecap="round"
              transform="rotate(-90 180 180)"
            />
          </svg>

          <div class="timer-display">
            <div class="timer-time">
              {{
                isBreakTime
                  ? formatTimerTime(breakTime)
                  : formatTimerTime(currentTime)
              }}
            </div>
          </div>
        </div>

        <div class="timer-controls">
          <!-- Pomodoro completed - show break options -->
          <template v-if="pomodoroCompleted && !isBreakTime">
            <button class="timer-button start-break" @click="startBreakTimer">
              Start Break
            </button>
            <button class="timer-button skip-break" @click="skipBreak">
              Skip Break
            </button>
          </template>

          <!-- Break timer controls -->
          <template v-else-if="isBreakTime">
            <button
              class="timer-button pause"
              @click="pauseBreakTimer"
              v-if="!isBreakPaused && isBreakRunning"
            >
              Pause
            </button>
            <button
              class="timer-button play"
              @click="resumeBreakTimer"
              v-else-if="isBreakPaused"
            >
              Play
            </button>
            <button class="timer-button skip-break" @click="skipBreak">
              Skip Break
            </button>
          </template>

          <!-- Regular pomodoro timer controls -->
          <template v-else>
            <button
              class="timer-button pause"
              @click="pauseTimer"
              v-if="!isPaused && isRunning"
            >
              Pause
            </button>
            <button
              class="timer-button play"
              @click="resumeTimer"
              v-else-if="isPaused"
            >
              Play
            </button>
            <button
              class="timer-button stop"
              @click="stopTimer"
              v-if="isRunning || isPaused"
            >
              Stop
            </button>
          </template>
        </div>
      </div>

      <!-- Right side focus stats -->
      <div class="focus-stats">
        <div class="focus-card">
          <div class="focus-label">Focus Time for Today</div>
          <div class="focus-value">{{ todayFocusTime }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Minimized Floating Timer -->
  <div v-else class="pomodoro-timer-minimized">
    <button class="maximize-button" @click="maximizeTimer">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M7 10l5 5 5-5z" />
      </svg>
    </button>

    <div class="minimized-time">
      {{
        isBreakTime
          ? isBreakRunning
            ? formatTimerTime(breakTime)
            : "Break Ready"
          : isRunning
          ? formatTimerTime(currentTime)
          : currentTask?.name || "No task"
      }}
    </div>

    <div
      class="minimized-controls"
      v-if="
        isRunning ||
        isPaused ||
        isBreakRunning ||
        isBreakPaused ||
        pomodoroCompleted
      "
    >
      <!-- Break timer controls -->
      <template v-if="isBreakTime">
        <button
          class="minimized-button pause"
          @click="pauseBreakTimer"
          v-if="!isBreakPaused && isBreakRunning"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
          </svg>
        </button>
        <button
          class="minimized-button play"
          @click="resumeBreakTimer"
          v-else-if="isBreakPaused"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        </button>
        <button class="minimized-button stop" @click="skipBreak">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <rect x="6" y="6" width="12" height="12" />
          </svg>
        </button>
      </template>

      <!-- Pomodoro completed - show break start option -->
      <template v-else-if="pomodoroCompleted">
        <button class="minimized-button start-break" @click="startBreakTimer">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        </button>
      </template>

      <!-- Regular pomodoro timer controls -->
      <template v-else>
        <button
          class="minimized-button pause"
          @click="pauseTimer"
          v-if="!isPaused && isRunning"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
          </svg>
        </button>
        <button
          class="minimized-button play"
          @click="resumeTimer"
          v-else-if="isPaused"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        </button>

        <button class="minimized-button stop" @click="stopTimer">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <rect x="6" y="6" width="12" height="12" />
          </svg>
        </button>
      </template>
    </div>
  </div>
</template>

<script>
import { ref, computed, onUnmounted, watch } from "vue";

export default {
  name: "PomodoroTimer",
  props: {
    task: {
      type: Object,
      default: null,
    },
    initialTime: {
      type: Number,
      default: 25 * 60, // 25 minutes in seconds
    },
  },
  emits: ["timer-complete", "timer-stop", "minimize", "maximize"],
  setup(props, { emit }) {
    const isMinimized = ref(false);
    const isRunning = ref(false);
    const isPaused = ref(false);
    const currentTime = ref(props.initialTime);
    const currentTask = ref(props.task);
    const timerInterval = ref(null);
    const todayFocusTime = ref("2h"); // This would be calculated from actual data

    // Break timer state
    const isBreakTime = ref(false);
    const isBreakRunning = ref(false);
    const isBreakPaused = ref(false);
    const breakTime = ref(5 * 60); // 5 minutes in seconds
    const pomodoroCompleted = ref(false);

    // Progress circle calculations
    const circumference = 2 * Math.PI * 140; // radius = 140
    const strokeDashoffset = computed(() => {
      if (isBreakTime.value) {
        const progress = (5 * 60 - breakTime.value) / (5 * 60);
        return circumference - progress * circumference;
      } else {
        const progress =
          (props.initialTime - currentTime.value) / props.initialTime;
        return circumference - progress * circumference;
      }
    });

    // Timer functions
    const startTimer = () => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }

      timerInterval.value = setInterval(() => {
        if (isBreakTime.value) {
          if (breakTime.value > 0) {
            breakTime.value--;
          } else {
            // Break finished
            stopBreakTimer();
          }
        } else {
          if (currentTime.value > 0) {
            currentTime.value--;
          } else {
            // Pomodoro finished
            stopTimer();
            pomodoroCompleted.value = true;
            emit("timer-complete", currentTask.value);
          }
        }
      }, 1000);
    };

    const pauseTimer = () => {
      isPaused.value = true;
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
    };

    const resumeTimer = () => {
      isPaused.value = false;
      startTimer();
    };

    const stopTimer = () => {
      isRunning.value = false;
      isPaused.value = false;
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
      // Reset timer to initial time
      currentTime.value = props.initialTime;
      pomodoroCompleted.value = false;
      emit("timer-stop");
    };

    // Break timer functions
    const startBreakTimer = () => {
      isBreakTime.value = true;
      isBreakRunning.value = true;
      isBreakPaused.value = false;
      pomodoroCompleted.value = false;
      breakTime.value = 5 * 60; // Reset to 5 minutes
      startTimer();
    };

    const pauseBreakTimer = () => {
      isBreakPaused.value = true;
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
    };

    const resumeBreakTimer = () => {
      isBreakPaused.value = false;
      startTimer();
    };

    const stopBreakTimer = () => {
      isBreakTime.value = false;
      isBreakRunning.value = false;
      isBreakPaused.value = false;
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
      breakTime.value = 5 * 60; // Reset break time
      pomodoroCompleted.value = false;
    };

    const skipBreak = () => {
      stopBreakTimer();
      pomodoroCompleted.value = false;
    };

    const minimizeTimer = () => {
      isMinimized.value = true;
      emit("minimize");
    };

    const maximizeTimer = () => {
      isMinimized.value = false;
      emit("maximize");
    };

    const formatTimerTime = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    };

    // Start timer when component is created with a task
    watch(
      () => props.task,
      (newTask) => {
        if (newTask) {
          currentTask.value = newTask;
          currentTime.value = props.initialTime;

          // Only start timer if the task is meant to be running
          if (newTask.isRunning !== false) {
            isRunning.value = true;
            isPaused.value = false;
            startTimer();
          } else {
            isRunning.value = false;
            isPaused.value = false;
          }
        }
      },
      { immediate: true }
    );

    onUnmounted(() => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
    });

    return {
      isMinimized,
      isRunning,
      isPaused,
      currentTime,
      currentTask,
      circumference,
      strokeDashoffset,
      todayFocusTime,
      // Break timer state
      isBreakTime,
      isBreakRunning,
      isBreakPaused,
      breakTime,
      pomodoroCompleted,
      // Timer functions
      pauseTimer,
      resumeTimer,
      stopTimer,
      // Break timer functions
      startBreakTimer,
      pauseBreakTimer,
      resumeBreakTimer,
      skipBreak,
      // UI functions
      minimizeTimer,
      maximizeTimer,
      formatTimerTime,
    };
  },
};
</script>

<style scoped>
.project-name-centered {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff4444;
  text-align: center;
  z-index: 1;
  min-width: 280px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  font-family: "Inter", sans-serif;
}

/* Fullscreen Timer Styles */
.pomodoro-timer-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #212121;
  color: white;
  display: flex;
  flex-direction: column;
  z-index: 2000;
}

.timer-header {
  display: flex;
  align-items: center;
  padding: 20px;
  gap: 20px;
}

.minimize-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.minimize-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.minimize-button svg {
  width: 24px;
  height: 24px;
}

.project-name {
  font-size: 1.2rem;
  font-weight: 500;
  color: #ff4444;
}

.timer-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 40px;
}

.timer-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.timer-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-progress {
  transition: stroke-dashoffset 0.3s ease;
}

.timer-display {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-time {
  font-size: 100px;
  font-weight: 300;
  color: #ff4444;
  font-family: "Inter", monospace;
}

.timer-controls {
  display: flex;
  gap: 20px;
}

.timer-button {
  background: #333;
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
  min-width: 80px;
}

.timer-button:hover {
  background: #444;
}

.timer-button.pause {
  background: #ff6b35;
}

.timer-button.pause:hover {
  background: #e55a2b;
}

.timer-button.play {
  background: #16d62c;
}

.timer-button.play:hover {
  background: #14c028;
}

.timer-button.stop {
  background: #ff4444;
}

.timer-button.stop:hover {
  background: #e63939;
}

.timer-button.start-break {
  background: #4caf50;
}

.timer-button.start-break:hover {
  background: #45a049;
}

.timer-button.skip-break {
  background: #ff9800;
}

.timer-button.skip-break:hover {
  background: #e68900;
}

.focus-stats {
  position: absolute;
  top: 100px; /* Aligns vertically with top of the timer ring */
  right: 120px; /* Moves it leftwards */
  transform: translateY(0); /* No vertical centering anymore */
}

.focus-card {
  background: #333;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  min-width: 200px;
}

.focus-label {
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 8px;
}

.focus-value {
  font-size: 2rem;
  font-weight: bold;
  color: #ff4444;
}

/* Minimized Timer Styles */
.pomodoro-timer-minimized {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1500;
  border: 1px solid #555;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  color: white;
  min-width: 200px;
}

.maximize-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
  flex-shrink: 0;
}

.maximize-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.maximize-button svg {
  width: 16px;
  height: 16px;
}

.minimized-time {
  font-size: 1rem;
  font-weight: 500;
  color: #ff4444;
  font-family: "Courier New", monospace;
  flex: 1;
  text-align: center;
}

.minimized-controls {
  display: flex;
  gap: 8px;
}

.minimized-button {
  background: #444;
  border: none;
  color: white;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.minimized-button:hover {
  background: #555;
}

.minimized-button svg {
  width: 14px;
  height: 14px;
}

.minimized-button.pause {
  background: #ff6b35;
}

.minimized-button.pause:hover {
  background: #e55a2b;
}

.minimized-button.play {
  background: #16d62c;
}

.minimized-button.play:hover {
  background: #14c028;
}

.minimized-button.stop {
  background: #ff4444;
}

.minimized-button.stop:hover {
  background: #e63939;
}
</style>
